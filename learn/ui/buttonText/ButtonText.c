#include "ButtonText.h"
#include <string.h>

// Static rendering resources shared by all buttons
static ButtonRenderResources s_resources = {0};

// Shader loading function
static SDL_GPUShader* load_shader(SDL_GPUDevice* device, const char* filename, SDL_GPUShaderStage stage) {
    char fullPath[256];
    SDL_snprintf(fullPath, sizeof(fullPath), "assets/Compiled/MSL/%s", filename);

    size_t codeSize;
    void* code = SDL_LoadFile(fullPath, &codeSize);
    if (code == NULL) {
        SDL_Log("Failed to load shader from disk! %s", fullPath);
        return NULL;
    }

    SDL_GPUShaderCreateInfo shaderInfo = {
        .code = code,
        .code_size = codeSize,
        .entrypoint = "main0", // MSL uses main0
        .format = SDL_GPU_SHADERFORMAT_MSL,
        .stage = stage,
        .num_samplers = 0,
        .num_uniform_buffers = (stage == SDL_GPU_SHADERSTAGE_VERTEX) ? 1 : (stage == SDL_GPU_SHADERSTAGE_FRAGMENT) ? 1 : 0,
        .num_storage_buffers = 0,
        .num_storage_textures = 0
    };

    SDL_GPUShader* shader = SDL_CreateGPUShader(device, &shaderInfo);
    if (shader == NULL) {
        SDL_Log("Failed to create shader!");
        SDL_free(code);
        return NULL;
    }

    SDL_free(code);
    return shader;
}

// Create button pipeline
static bool create_button_pipeline(ButtonRenderResources* resources, SDL_Window* window) {
    // Load shaders
    resources->vertex_shader = load_shader(
        resources->device, "ButtonUniform.vert.msl", SDL_GPU_SHADERSTAGE_VERTEX);
    if (resources->vertex_shader == NULL) {
        SDL_Log("Failed to create button vertex shader!");
        return false;
    }

    resources->fragment_shader = load_shader(
        resources->device, "ButtonUniform.frag.msl", SDL_GPU_SHADERSTAGE_FRAGMENT);
    if (resources->fragment_shader == NULL) {
        SDL_Log("Failed to create button fragment shader!");
        SDL_ReleaseGPUShader(resources->device, resources->vertex_shader);
        resources->vertex_shader = NULL;
        return false;
    }

    // Create pipeline
    SDL_GPUGraphicsPipelineCreateInfo pipelineCreateInfo = {
        .target_info = {
            .num_color_targets = 1,
            .color_target_descriptions = (SDL_GPUColorTargetDescription[]){
                {
                    .format = SDL_GetGPUSwapchainTextureFormat(resources->device, window),
                    .blend_state = (SDL_GPUColorTargetBlendState){
                        .enable_blend = true,
                        .alpha_blend_op = SDL_GPU_BLENDOP_ADD,
                        .color_blend_op = SDL_GPU_BLENDOP_ADD,
                        .color_write_mask = 0xF,
                        .src_alpha_blendfactor = SDL_GPU_BLENDFACTOR_SRC_ALPHA,
                        .dst_alpha_blendfactor = SDL_GPU_BLENDFACTOR_DST_ALPHA,
                        .src_color_blendfactor = SDL_GPU_BLENDFACTOR_SRC_ALPHA,
                        .dst_color_blendfactor = SDL_GPU_BLENDFACTOR_ONE_MINUS_SRC_ALPHA
                    }
                }
            },
        },
        // This is set up to match the vertex shader layout!
        .vertex_input_state = (SDL_GPUVertexInputState){
            .num_vertex_buffers = 1,
            .vertex_buffer_descriptions = (SDL_GPUVertexBufferDescription[]){{
                .slot = 0,
                .input_rate = SDL_GPU_VERTEXINPUTRATE_VERTEX,
                .pitch = sizeof(PositionVertex)
            }},
            .num_vertex_attributes = 1, // Position only (2D)
            .vertex_attributes = (SDL_GPUVertexAttribute[]){
                {
                    .buffer_slot = 0,
                    .format = SDL_GPU_VERTEXELEMENTFORMAT_FLOAT2,
                    .location = 0,
                    .offset = 0
                }
            }
        },
        .primitive_type = SDL_GPU_PRIMITIVETYPE_TRIANGLELIST,
        .vertex_shader = resources->vertex_shader,
        .fragment_shader = resources->fragment_shader
    };

    resources->pipeline = SDL_CreateGPUGraphicsPipeline(resources->device, &pipelineCreateInfo);
    if (resources->pipeline == NULL) {
        SDL_Log("Failed to create button pipeline: %s", SDL_GetError());
        SDL_ReleaseGPUShader(resources->device, resources->vertex_shader);
        SDL_ReleaseGPUShader(resources->device, resources->fragment_shader);
        resources->vertex_shader = NULL;
        resources->fragment_shader = NULL;
        return false;
    }

    return true;
}

// Create shared quad vertex data (unit quad from 0,0 to 1,1)
static void create_shared_quad(PositionVertex *vertices, Uint16 *indices) {
    // Unit quad vertices (will be transformed by matrix)
    vertices[0] = (PositionVertex){0.0f, 0.0f}; // Top-left
    vertices[1] = (PositionVertex){1.0f, 0.0f}; // Top-right
    vertices[2] = (PositionVertex){1.0f, 1.0f}; // Bottom-right
    vertices[3] = (PositionVertex){0.0f, 1.0f}; // Bottom-left

    // Indices for two triangles
    indices[0] = 0; indices[1] = 1; indices[2] = 2;
    indices[3] = 0; indices[4] = 2; indices[5] = 3;
}

// Initialize button rendering resources
bool Button_InitRenderResources(SDL_GPUDevice* device, SDL_Window* window) {
    if (s_resources.initialized) {
        return true; // Already initialized
    }

    s_resources.device = device;

    // Create shared quad data (4 vertices, 6 indices)
    PositionVertex vertices[4];
    Uint16 indices[6];
    create_shared_quad(vertices, indices);

    size_t vertex_data_size = sizeof(PositionVertex) * 4;
    size_t index_data_size = sizeof(Uint16) * 6;

    // Create vertex buffer
    s_resources.vertex_buffer = SDL_CreateGPUBuffer(
        device,
        &(SDL_GPUBufferCreateInfo){
            .usage = SDL_GPU_BUFFERUSAGE_VERTEX,
            .size = vertex_data_size
        }
    );

    if (!s_resources.vertex_buffer) {
        SDL_Log("Failed to create button vertex buffer!");
        Button_CleanupRenderResources();
        return false;
    }

    // Create index buffer
    s_resources.index_buffer = SDL_CreateGPUBuffer(
        device,
        &(SDL_GPUBufferCreateInfo){
            .usage = SDL_GPU_BUFFERUSAGE_INDEX,
            .size = index_data_size
        }
    );

    if (!s_resources.index_buffer) {
        SDL_Log("Failed to create button index buffer!");
        Button_CleanupRenderResources();
        return false;
    }

    // Create transfer buffer
    s_resources.transfer_buffer = SDL_CreateGPUTransferBuffer(
        device,
        &(SDL_GPUTransferBufferCreateInfo){
            .usage = SDL_GPU_TRANSFERBUFFERUSAGE_UPLOAD,
            .size = vertex_data_size + index_data_size
        }
    );

    if (!s_resources.transfer_buffer) {
        SDL_Log("Failed to create button transfer buffer!");
        Button_CleanupRenderResources();
        return false;
    }

    // Create button pipeline first
    if (!create_button_pipeline(&s_resources, window)) {
        SDL_Log("Failed to create button pipeline!");
        Button_CleanupRenderResources();
        return false;
    }

    // Upload shared quad data to GPU buffers
    void *transfer_data = SDL_MapGPUTransferBuffer(device, s_resources.transfer_buffer, false);
    if (!transfer_data) {
        SDL_Log("Failed to map button transfer buffer!");
        Button_CleanupRenderResources();
        return false;
    }

    // Copy vertex and index data
    memcpy(transfer_data, vertices, vertex_data_size);
    memcpy((char*)transfer_data + vertex_data_size, indices, index_data_size);
    SDL_UnmapGPUTransferBuffer(device, s_resources.transfer_buffer);

    s_resources.initialized = true;

    // Note: The shared vertex data will be uploaded when the first command buffer is available
    // This is done in the Button_UploadSharedData function which should be called once
    return true;
}

// Upload shared quad data to GPU (call this once with a command buffer)
bool Button_UploadSharedData(SDL_GPUCommandBuffer* cmd_buf) {
    if (!s_resources.initialized || !cmd_buf) {
        return false;
    }

    // Create shared quad data
    PositionVertex vertices[4];
    Uint16 indices[6];
    create_shared_quad(vertices, indices);

    size_t vertex_data_size = sizeof(PositionVertex) * 4;
    size_t index_data_size = sizeof(Uint16) * 6;

    // Map transfer buffer
    void *transfer_data = SDL_MapGPUTransferBuffer(s_resources.device, s_resources.transfer_buffer, false);
    if (!transfer_data) {
        SDL_Log("Failed to map button transfer buffer for upload!");
        return false;
    }

    // Copy vertex and index data
    memcpy(transfer_data, vertices, vertex_data_size);
    memcpy((char*)transfer_data + vertex_data_size, indices, index_data_size);
    SDL_UnmapGPUTransferBuffer(s_resources.device, s_resources.transfer_buffer);

    // Start copy pass
    SDL_GPUCopyPass *copy_pass = SDL_BeginGPUCopyPass(cmd_buf);

    // Upload vertex data
    SDL_UploadToGPUBuffer(
        copy_pass,
        &(SDL_GPUTransferBufferLocation){
            .transfer_buffer = s_resources.transfer_buffer,
            .offset = 0
        },
        &(SDL_GPUBufferRegion){
            .buffer = s_resources.vertex_buffer,
            .offset = 0,
            .size = vertex_data_size
        },
        false
    );

    // Upload index data
    SDL_UploadToGPUBuffer(
        copy_pass,
        &(SDL_GPUTransferBufferLocation){
            .transfer_buffer = s_resources.transfer_buffer,
            .offset = vertex_data_size
        },
        &(SDL_GPUBufferRegion){
            .buffer = s_resources.index_buffer,
            .offset = 0,
            .size = index_data_size
        },
        false
    );

    SDL_EndGPUCopyPass(copy_pass);
    return true;
}

// Clean up button rendering resources
void Button_CleanupRenderResources() {
    if (s_resources.pipeline) {
        SDL_ReleaseGPUGraphicsPipeline(s_resources.device, s_resources.pipeline);
        s_resources.pipeline = NULL;
    }
    
    if (s_resources.vertex_shader) {
        SDL_ReleaseGPUShader(s_resources.device, s_resources.vertex_shader);
        s_resources.vertex_shader = NULL;
    }
    
    if (s_resources.fragment_shader) {
        SDL_ReleaseGPUShader(s_resources.device, s_resources.fragment_shader);
        s_resources.fragment_shader = NULL;
    }
    
    if (s_resources.transfer_buffer) {
        SDL_ReleaseGPUTransferBuffer(s_resources.device, s_resources.transfer_buffer);
        s_resources.transfer_buffer = NULL;
    }

    if (s_resources.vertex_buffer) {
        SDL_ReleaseGPUBuffer(s_resources.device, s_resources.vertex_buffer);
        s_resources.vertex_buffer = NULL;
    }

    if (s_resources.index_buffer) {
        SDL_ReleaseGPUBuffer(s_resources.device, s_resources.index_buffer);
        s_resources.index_buffer = NULL;
    }
    
    s_resources.initialized = false;
}

// Calculate and set text position for center alignment
static void Button_CalculateTextPosition(Button *button, float button_center_x, float button_center_y,
                                        int text_width, int text_height __attribute__((unused))) {
    if (!button || !button->text_object) return;

    TTF_Font *font = button->text_object->font;

    // Simple horizontal centering - position text so its left edge starts at the right position
    // to center the original text width (letter spacing will be applied during rendering)
    float text_x = button_center_x - (float)text_width / 2.0f;

    // For Y positioning, use font metrics to center the text vertically
    int font_ascent = TTF_GetFontAscent(font);
    int font_descent = TTF_GetFontDescent(font);

    // Calculate the visual center offset from the baseline
    // Ascent is positive (above baseline), descent is negative (below baseline)
    // Visual center is halfway between the top (ascent) and bottom (descent)
    float visual_center_offset = (font_ascent + font_descent) / 2.0f;

    // Position the text so its visual center aligns with the button center
    // We subtract the visual center offset to move the baseline to the right position
    float text_y = button_center_y - visual_center_offset;

    // Apply the calculated position to the text object
    UIText_SetTextPosition(button->text_object, text_x, text_y);
}

// Initialize a button with default properties (now includes text creation)
void Button_Init(Button *button, float x, float y, float width, float height, const char *text, UITextContext* text_context, TTF_Font* font) {
    button->rect = (SDL_FRect){x, y, width, height};
    button->color = (SDL_FColor){0.4f, 0.4f, 0.8f, 1.0f};       // Blue
    button->hoverColor = (SDL_FColor){0.5f, 0.5f, 0.9f, 1.0f};  // Lighter blue
    button->textColor = (SDL_FColor){1.0f, 1.0f, 1.0f, 1.0f};   // White
    button->text = (char *)text;
    button->isHovered = false;
    button->isPressed = false;
    button->isDirty = true;  // Initially dirty to ensure first render
    button->letter_spacing = 0.8f;  // Default: 20% tighter letter spacing

    // Create text object positioned at the center of the button
    if (text_context && font && text) {
        // Create text object first with temporary position
        button->text_object = UIText_CreateText(text_context, font, text, 0.0f, 0.0f, button->textColor);
        if (!button->text_object) {
            SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to create button text object");
        } else {
            // Get actual text dimensions and center it properly
            int text_width, text_height;
            if (TTF_GetTextSize(button->text_object->text, &text_width, &text_height)) {
                // Calculate button center
                float button_center_x = x + width / 2.0f;
                float button_center_y = y + height / 2.0f;

                // Set letter spacing from button property FIRST
                UIText_SetLetterSpacing(button->text_object, button->letter_spacing);

                // Then calculate and set text position with the correct letter spacing
                Button_CalculateTextPosition(button, button_center_x, button_center_y,
                                           text_width, text_height);
            } else {
                SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to get text size for centering");
                // Fallback to approximate center
                float text_x = x + width/2.0f - 30.0f;
                float text_y = y + height/2.0f - 12.0f;
                UIText_SetTextPosition(button->text_object, text_x, text_y);
                UIText_SetLetterSpacing(button->text_object, 0.8f);
            }
        }
    } else {
        button->text_object = NULL;
    }
}

// Internal function to recalculate text position
static void Button_RecalculateTextPosition(Button *button) {
    if (button && button->text_object) {
        // Get actual text dimensions and center it properly
        int text_width, text_height;
        if (TTF_GetTextSize(button->text_object->text, &text_width, &text_height)) {
            // Calculate button center
            float button_center_x = button->rect.x + button->rect.w / 2.0f;
            float button_center_y = button->rect.y + button->rect.h / 2.0f;

            // Use the helper function to calculate and set text position
            Button_CalculateTextPosition(button, button_center_x, button_center_y,
                                       text_width, text_height);
        }
    }
}

// Update button position (e.g., after window resize)
void Button_UpdatePosition(Button *button, float x, float y) {
    if (button->rect.x != x || button->rect.y != y) {
        button->rect.x = x;
        button->rect.y = y;
        button->isDirty = true;
    }

    // Always recalculate text position when this function is called
    Button_RecalculateTextPosition(button);
}

// Helper function to set letter spacing and recalculate position
static void Button_SetLetterSpacingAndRecalculate(Button* button, float spacing) {
    if (button && button->text_object) {
        // Set the spacing values FIRST
        button->letter_spacing = spacing;
        UIText_SetLetterSpacing(button->text_object, spacing);

        // Then recalculate text position (UITextObject.x) using the new spacing
        // This will adjust the text position within the fixed button rectangle
        Button_RecalculateTextPosition(button);
        button->isDirty = true;
    }
}

// Set button letter spacing and update text positioning
void Button_SetLetterSpacing(Button* button, float spacing) {
    Button_SetLetterSpacingAndRecalculate(button, spacing);
}

// Check if a point is inside the button
bool Button_ContainsPoint(Button *button, float x, float y) {
    return (x >= button->rect.x && x <= button->rect.x + button->rect.w &&
            y >= button->rect.y && y <= button->rect.y + button->rect.h);
}

// Handle mouse motion event
void Button_HandleMouseMotion(Button *button, float x, float y) {
    bool wasHovered = button->isHovered;
    button->isHovered = Button_ContainsPoint(button, x, y);

    // Mark as dirty if hover state changed
    if (wasHovered != button->isHovered) {
        button->isDirty = true;
    }
}

// Handle mouse button down event
bool Button_HandleMouseButtonDown(Button *button, float x, float y, Uint8 button_type) {
    if (button_type == SDL_BUTTON_LEFT) {
        if (Button_ContainsPoint(button, x, y)) {
            if (!button->isPressed) {
                button->isPressed = true;
                button->isDirty = true;
                return true;
            }
            return true;
        }
    }
    return false;
}

// Handle mouse button up event
bool Button_HandleMouseButtonUp(Button *button, float x, float y, Uint8 button_type) {
    bool wasClicked = false;

    if (button_type == SDL_BUTTON_LEFT && button->isPressed) {
        if (Button_ContainsPoint(button, x, y)) {
            // Button clicked!
            wasClicked = true;
        }
        button->isPressed = false;
        button->isDirty = true;
    }

    return wasClicked;
}

// Get the current color of the button (based on hover state)
SDL_FColor Button_GetCurrentColor(Button *button) {
    return button->isHovered ? button->hoverColor : button->color;
}

// Create transform matrix for button positioning and scaling (internal function)
static void Button_CreateTransformMatrix(Button* button, int screen_width, int screen_height, float* matrix) {
    // Convert screen coordinates to NDC
    float ndc_x = (button->rect.x / screen_width) * 2.0f - 1.0f;
    // Adjust Y position so that button->rect.y represents the top-left corner
    float ndc_y = 1.0f - ((button->rect.y + button->rect.h) / screen_height) * 2.0f; // Flip Y axis, account for height
    float ndc_width = (button->rect.w / screen_width) * 2.0f;
    float ndc_height = (button->rect.h / screen_height) * 2.0f;

    // Create transform matrix (column-major order for OpenGL/Metal)
    // Scale and translate the unit quad to button position and size
    matrix[0] = ndc_width;  matrix[4] = 0.0f;       matrix[8] = 0.0f;  matrix[12] = ndc_x;
    matrix[1] = 0.0f;       matrix[5] = ndc_height; matrix[9] = 0.0f;  matrix[13] = ndc_y;
    matrix[2] = 0.0f;       matrix[6] = 0.0f;       matrix[10] = 1.0f; matrix[14] = 0.0f;
    matrix[3] = 0.0f;       matrix[7] = 0.0f;       matrix[11] = 0.0f; matrix[15] = 1.0f;
}

// Set button color uniform using push constants (internal function)
static void Button_SetColorUniform(SDL_GPUCommandBuffer* cmd_buf, Button* button) {
    if (!cmd_buf || !button) {
        return;
    }

    // Get current button color based on state
    SDL_FColor buttonColor = Button_GetCurrentColor(button);

    // Create uniform data
    ButtonColorUniform colorUniform = {
        .r = buttonColor.r,
        .g = buttonColor.g,
        .b = buttonColor.b,
        .a = buttonColor.a
    };

    // Push uniform data to fragment shader
    SDL_PushGPUFragmentUniformData(cmd_buf, 0, &colorUniform, sizeof(ButtonColorUniform));
}

// Set button transform uniform (internal function)
static void Button_SetTransformUniform(SDL_GPUCommandBuffer* cmd_buf, Button* button, int screen_width, int screen_height) {
    if (!cmd_buf || !button) {
        return;
    }

    // Create transform matrix
    ButtonTransformUniform transformUniform;
    Button_CreateTransformMatrix(button, screen_width, screen_height, transformUniform.transform);

    // Push uniform data to vertex shader
    SDL_PushGPUVertexUniformData(cmd_buf, 0, &transformUniform, sizeof(ButtonTransformUniform));
}

// Update button text data (call outside render pass)
void Button_Update(Button* buttons, int button_count, UITextContext* text_context, SDL_GPUCommandBuffer* cmd_buf) {
    if (!buttons || button_count <= 0 || !text_context || !cmd_buf) {
        return;
    }

    // Collect all text objects from buttons
    UITextObject* text_objects[button_count];
    int text_count = 0;

    for (int i = 0; i < button_count; i++) {
        if (buttons[i].text_object) {
            text_objects[text_count++] = buttons[i].text_object;
        }
    }

    // Prepare text data if we have any text objects
    if (text_count > 0) {
        UIText_PrepareTextData(text_context, text_objects, text_count, cmd_buf);
    }
}

// Draw buttons using the provided render pass and command buffer (now includes text rendering)
void Button_Draw(SDL_GPURenderPass* render_pass, SDL_GPUCommandBuffer* cmd_buf, SDL_GPUGraphicsPipeline* external_pipeline, Button* buttons, int button_count, int screen_width, int screen_height, UITextContext* text_context) {
    if (!s_resources.initialized || !render_pass || !cmd_buf || !buttons || button_count <= 0) {
        return;
    }

    // Use our custom pipeline if available, otherwise use the provided one
    SDL_GPUGraphicsPipeline* pipeline = s_resources.pipeline ? s_resources.pipeline : external_pipeline;

    if (!pipeline) {
        SDL_Log("No valid pipeline for button rendering!");
        return;
    }

    // Bind pipeline and shared buffers once
    SDL_BindGPUGraphicsPipeline(render_pass, pipeline);
    SDL_BindGPUVertexBuffers(
        render_pass, 0,
        &(SDL_GPUBufferBinding){.buffer = s_resources.vertex_buffer, .offset = 0},
        1
    );
    SDL_BindGPUIndexBuffer(
        render_pass,
        &(SDL_GPUBufferBinding){.buffer = s_resources.index_buffer, .offset = 0},
        SDL_GPU_INDEXELEMENTSIZE_16BIT
    );

    // Draw each button with its own transform and color uniforms
    for (int i = 0; i < button_count; i++) {
        // Set transform uniform for this button
        Button_SetTransformUniform(cmd_buf, &buttons[i], screen_width, screen_height);

        // Set color uniform for this button
        Button_SetColorUniform(cmd_buf, &buttons[i]);

        // Draw the shared quad (same 6 indices for all buttons)
        SDL_DrawGPUIndexedPrimitives(render_pass, 6, 1, 0, 0, 0);
    }

    // Render text on top of all buttons within the same render pass
    if (text_context) {
        // Collect all text objects from buttons
        UITextObject* text_objects[button_count];
        int text_count = 0;

        for (int i = 0; i < button_count; i++) {
            if (buttons[i].text_object) {
                text_objects[text_count++] = buttons[i].text_object;
            }
        }

        // Render text if we have any text objects
        if (text_count > 0) {
            UIText_RenderInRenderPass(text_context, text_objects, text_count, render_pass, cmd_buf);
        }
    }
}

// Clean up button text resources
void Button_DestroyText(Button* button) {
    if (button && button->text_object) {
        UIText_DestroyText(button->text_object);
        button->text_object = NULL;
    }
}