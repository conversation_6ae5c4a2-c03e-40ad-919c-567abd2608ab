{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "sdl3_pong", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "sdl3_pong::@6890427a1f51a3e7e1df", "jsonFile": "target-sdl3_pong-Debug-89f36ed3d6a8c5d0da9b.json", "name": "sdl3_pong", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build", "source": "/Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice"}, "version": {"major": 2, "minor": 8}}